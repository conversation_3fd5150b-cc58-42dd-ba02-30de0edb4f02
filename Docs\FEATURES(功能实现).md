# 神灯AI·灵阅 V0.49 - 功能实现文档

本文档详细记录已开发完成功能的实现方式、关键代码和使用示例。

## 🎯 核心功能模块

### 1. 智能连续阅读系统

#### 功能描述
自动识别网页中的"下一页"/"下一章"链接，实现无缝的连续播放体验。支持章节计数控制和时长控制。

#### 实现方式
- **内容解析**: 使用Mozilla Readability库提取页面主要内容
- **链接识别**: 通过关键词匹配和URL模式识别下一页链接
- **状态管理**: Service Worker管理播放状态和章节计数
- **后台播放**: 支持用户切换页面后继续播放

#### 关键代码
```javascript
// 查找下一页链接 (src/background/index.js)
function findNextPageLinkSimple() {
  const prioritizedKeywords = [
    '下一页', '下一章', 'next page', 'next chapter',
    '下页', '下章', '下一节', 'next section', '下节',
    '>>>', '»', '→', '▶', '▷', 'next', '下一个', '继续阅读'
  ];

  const links = Array.from(document.querySelectorAll('a[href]'));
  for (const keyword of prioritizedKeywords) {
    for (const link of links) {
      const text = link.textContent.trim().toLowerCase();
      if (text.includes(keyword.toLowerCase())) {
        return link.href;
      }
    }
  }
  return null;
}

// 连续播放控制 (src/background/index.js)
async function navigateToNextChapter() {
  currentChapterCount++;

  if (currentChapterCount >= continuousChapters) {
    console.log(`已读完设定的 ${continuousChapters} 章，停止连续阅读`);
    readingState = 'idle';
    return;
  }

  const nextChapterUrl = await findNextChapterInCurrentPage();
  if (nextChapterUrl) {
    // 导航到下一章并继续播放
    chrome.tabs.update(currentReadingTabId, { url: nextChapterUrl });
  }
}
```

#### 使用示例
1. 打开小说网站任意章节页面
2. 点击扩展图标打开侧边栏
3. 设置章节数量限制 (如30章)
4. 点击播放按钮开始连续播放
5. 系统自动识别下一章链接并继续播放

#### 注意事项
- 不同网站的链接结构可能不同，系统会尝试多种识别方式
- 建议在稳定的网络环境下使用
- 某些动态加载的网站可能需要额外的处理时间

### 2. 语音朗读系统

#### 功能描述
集成浏览器内置TTS引擎，提供高质量的语音朗读功能，支持语音选择、语速调节和音量控制。

#### 实现方式
- **TTS集成**: 使用Chrome TTS API进行语音合成
- **语音管理**: 自动获取系统可用语音，支持男声/女声标识
- **实时控制**: 支持播放/暂停、语速调节 (0.5x-10x)
- **设置持久化**: 自动保存用户偏好设置

#### 关键代码
```javascript
// TTS播放控制 (src/background/index.js)
function speakText(text, tabId, startPosition = 0) {
  const utterance = {
    text: text,
    options: {
      voiceName: currentVoice,
      rate: currentSpeed,
      volume: 1.0,
      pitch: 1.0
    },
    position: startPosition
  };

  chrome.tts.speak(text, {
    voiceName: currentVoice,
    rate: currentSpeed,
    volume: 1.0,
    onEvent: (event) => {
      if (event.type === 'end') {
        handleTTSEnd(tabId);
      } else if (event.type === 'error') {
        console.error("TTS播放出错:", event);
      }
    }
  });
}

// 语音列表获取 (src/ui/sidepanel/index.js)
function loadAvailableVoices() {
  chrome.tts.getVoices((voices) => {
    const voicesList = document.getElementById('available-voices-list');
    voices.forEach(voice => {
      const voiceItem = document.createElement('div');
      voiceItem.className = 'voice-item';

      // 识别男声/女声
      const gender = identifyVoiceGender(voice.voiceName);
      voiceItem.innerHTML = `
        <span class="voice-name">${voice.voiceName}</span>
        <span class="voice-gender ${gender}">${gender === 'male' ? '♂' : '♀'}</span>
      `;

      voicesList.appendChild(voiceItem);
    });
  });
}
```

#### 使用示例
1. 在侧边栏点击语音设置按钮
2. 选择偏好的语音 (支持试听)
3. 调节语速到合适的倍数
4. 设置会自动保存，下次使用时生效

### 3. URL播放功能

#### 功能描述
支持直接输入网址播放任意网页内容，包括播放历史管理、智能去重和状态控制。

#### 实现方式
- **URL验证**: 实时验证输入的URL格式
- **异步播放**: 在后台标签页中加载和播放内容
- **历史管理**: 自动记录播放历史，支持快速重播
- **状态控制**: 播放时锁定输入，停止时自动清理

#### 关键代码
```javascript
// URL播放处理 (src/background/index.js)
async function handlePlayURL(url) {
  try {
    // 验证URL格式
    const urlObj = new URL(url);
    if (urlObj.protocol !== 'http:' && urlObj.protocol !== 'https:') {
      throw new Error("不支持的URL协议");
    }

    // 停止当前播放
    if (readingState === 'reading' || readingState === 'paused') {
      stopTTSCompletely("play_url");
    }

    // 创建新标签页播放
    const targetTab = await chrome.tabs.create({
      url: url,
      active: false // 后台播放
    });

    // 等待页面加载并解析内容
    await waitForContentParsed(targetTab.id);

    // 开始播放
    const article = tabContentCache[targetTab.id];
    if (article && article.textContent) {
      await addURLPlaybackHistory(url, article);
      continuousSpeakText(article.textContent, targetTab.id);
      return { success: true, title: article.title };
    }
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// 播放历史管理 (src/ui/sidepanel/index.js)
function updatePlaybackHistoryDisplay() {
  const historyContent = document.getElementById('history-content');
  historyContent.innerHTML = '';

  playbackHistory.forEach((item, index) => {
    const historyItem = document.createElement('div');
    historyItem.className = 'history-item';
    historyItem.innerHTML = `
      <div class="history-info">
        <div class="history-title">${item.title}</div>
        <div class="history-meta">
          <span class="history-chapter">${item.chapter || '未知章节'}</span>
          <span class="history-time">${formatTime(item.timestamp)}</span>
        </div>
      </div>
      <div class="history-actions">
        <button onclick="playHistoryItem(${index})" class="history-play-btn">播放</button>
        <button onclick="deleteHistoryItem(${index})" class="history-delete-btn">删除</button>
      </div>
    `;
    historyContent.appendChild(historyItem);
  });
}
```

#### 使用示例
1. 在侧边栏URL输入框中输入网址
2. 点击播放按钮开始播放
3. 系统自动记录到播放历史
4. 可在历史记录中快速重播或删除

### 4. 用户界面系统

#### 功能描述
提供紧凑型侧边栏界面，包括播放控制、设置面板、历史管理等功能。

#### 实现方式
- **侧边栏设计**: 使用Chrome Side Panel API
- **响应式布局**: 适配不同屏幕尺寸
- **实时状态**: 显示播放进度和章节信息
- **模块化CSS**: 分离样式文件，便于维护

#### 关键代码
```javascript
// 状态更新显示 (src/ui/sidepanel/index.js)
function updateUI(state) {
  // 更新播放按钮状态
  if (state.readingState === 'reading') {
    playPauseButton.classList.add('playing');
    playPauseButton.title = "暂停播放";
  } else {
    playPauseButton.classList.remove('playing');
    playPauseButton.title = "点击直接播放当前页";
  }

  // 更新状态指示器
  statusIndicatorEl.textContent = translateState(state.readingState);
  statusIndicatorEl.className = `status-indicator ${state.readingState}`;

  // 更新章节信息
  if (state.currentChapterTitle) {
    chapterTitleEl.textContent = state.currentChapterTitle;
  }

  // 控制URL输入框状态
  const isPlayingOrPaused = ['reading', 'paused', 'navigating'].includes(state.readingState);
  if (smartUrlInput) {
    smartUrlInput.disabled = isPlayingOrPaused;
    smartUrlInput.placeholder = isPlayingOrPaused
      ? '需要停止播放后才能输入网址'
      : '输入网址播放指定内容，留空播放当前页面';
  }
}
```

#### 使用示例
1. 点击浏览器工具栏中的扩展图标
2. 侧边栏自动打开显示控制界面
3. 所有功能都可通过侧边栏操作
4. 界面会实时反映当前播放状态

## 🔧 技术实现细节

### 状态管理
- 使用Service Worker作为状态中心
- Chrome Storage API持久化用户设置
- 消息传递机制同步前后端状态

### 内容解析
- Mozilla Readability库提取主要内容
- 多种策略识别下一页链接
- 支持各类小说网站的页面结构

### 性能优化
- 按需加载和解析内容
- 智能缓存机制减少重复请求
- 异步处理避免界面阻塞

### 错误处理
- 完善的错误捕获和用户提示
- 网络异常的重试机制
- 状态恢复和清理逻辑

## 📊 功能完成度

| 功能模块 | 完成度 | 状态 |
|---------|--------|------|
| 智能连续阅读 | 100% | ✅ 已完成 |
| 语音朗读系统 | 100% | ✅ 已完成 |
| URL播放功能 | 100% | ✅ 已完成 |
| 用户界面 | 100% | ✅ 已完成 |
| 设置管理 | 100% | ✅ 已完成 |
| 播放历史 | 100% | ✅ 已完成 |
| 第三方TTS | 0% | 🔄 规划中 |
| AI功能 | 0% | 🔄 规划中 |
