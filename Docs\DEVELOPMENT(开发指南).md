# 神灯AI·灵阅 V0.49 - 开发指南

## 🛠️ 开发环境搭建

### 系统要求
- **操作系统**: Windows 10+, macOS 10.14+, Linux (Ubuntu 18.04+)
- **浏览器**: Chrome 88+ 或 Edge 88+ (开发者模式)
- **编辑器**: VS Code (推荐) 或其他支持JavaScript的IDE
- **Node.js**: 16+ (可选，用于包管理和构建工具)

### 环境配置步骤
1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd 神灯AI·灵阅
   ```

2. **安装依赖** (可选)
   ```bash
   npm install  # 如果使用npm管理依赖
   ```

3. **加载扩展到浏览器**
   - 打开Chrome/Edge浏览器
   - 访问 `chrome://extensions/` 或 `edge://extensions/`
   - 启用"开发者模式"
   - 点击"加载已解压的扩展程序"
   - 选择项目根目录

4. **开发工具配置**
   - 安装VS Code扩展：JavaScript (ES6) code snippets
   - 配置代码格式化工具 (Prettier推荐)
   - 启用ESLint进行代码检查

## 📁 项目结构详解

### 核心文件说明
```
神灯AI·灵阅/
├── manifest.json          # 扩展配置文件 (Manifest V3)
├── package.json           # 项目配置
├── README.md              # 项目说明
├── src/                   # 源代码目录
│   ├── background/        # 后台脚本 (Service Worker)
│   │   └── index.js       # 主后台脚本 (3600+行)
│   ├── content/           # 内容脚本
│   │   └── parser.js      # 页面解析器 (Readability集成)
│   ├── ui/                # 用户界面
│   │   ├── sidepanel/     # 主侧边栏界面
│   │   │   ├── index.html # 界面结构
│   │   │   ├── index.js   # 界面逻辑 (4400+行)
│   │   │   └── *.css      # 样式文件
│   │   ├── options/       # 选项设置页面
│   │   └── voiceSettings/ # 语音设置面板
│   ├── lib/               # 第三方库
│   │   └── Readability.js # Mozilla内容提取库
│   └── assets/            # 静态资源
│       └── icons/         # 扩展图标
├── Docs/                  # 项目文档
├── 存档/                  # 历史文档存档
└── test-*.html            # 测试页面
```

### 关键模块职责

#### 1. Service Worker (src/background/index.js)
- **播放控制**: TTS播放、暂停、停止
- **状态管理**: 播放状态、用户设置
- **消息处理**: 与UI和内容脚本通信
- **连续播放**: 自动章节切换逻辑
- **URL播放**: 后台标签页管理

#### 2. 侧边栏界面 (src/ui/sidepanel/)
- **用户交互**: 播放控制、设置调节
- **状态显示**: 实时播放状态和进度
- **URL管理**: 输入播放、历史记录
- **语音设置**: 语音选择和试听

#### 3. 内容脚本 (src/content/)
- **页面解析**: 提取文章主要内容
- **链接识别**: 查找下一页/章节链接
- **DOM操作**: 页面内容处理

## 🔧 开发工作流

### 日常开发流程
1. **修改代码**: 在src目录下编辑相应文件
2. **重新加载扩展**: 在浏览器扩展页面点击刷新
3. **测试功能**: 在测试页面或实际网站验证
4. **调试问题**: 使用浏览器开发者工具
5. **提交代码**: 确保功能正常后提交

### 调试技巧
```javascript
// Service Worker调试
console.log("BG: 调试信息", data);

// 侧边栏调试
console.log("UI: 界面状态", state);

// 内容脚本调试
console.log("Content: 页面信息", pageData);
```

### 测试方法
1. **功能测试**: 使用test-*.html页面
2. **兼容性测试**: 在不同网站验证
3. **性能测试**: 监控内存和CPU使用
4. **错误处理**: 测试异常情况

## 目录结构 (Directory Structure)

(来自 系统架构设计.md)
```
神灯AI朗读增强助手/
├── docs/                  # 项目文档 (重构后)
│   ├── README.md
│   ├── REQUIREMENTS.md
│   ├── DESIGN.md
│   ├── DEVELOPMENT.md
│   ├── FEATURES.md
│   └── CHANGELOG.md
├── dist/                  # 打包后的扩展文件 (构建生成, Git忽略)
├── scripts/               # 构建、部署等辅助脚本
│   └── build.js           # (示例)
├── src/                   # 源代码
│   ├── background/        # Service Worker 相关代码
│   │   └── index.js       # Service Worker 入口
│   ├── content/           # Content Scripts 相关代码
│   │   ├── parser.js      # 页面内容解析逻辑
│   │   └── injector.js    # 注入到页面的主脚本
│   ├── core/              # 核心功能模块 (或称 lib/modules)
│   │   ├── tts-manager.js
│   │   ├── llm-manager.js
│   │   ├── state-manager.js
│   │   ├── wiki-builder.js
│   │   └── role-recognizer.js
│   ├── ui/                # 用户界面相关代码
│   │   ├── components/    # (若使用框架) 可复用UI组件
│   │   │   └── PlayerControls.jsx # (示例)
│   │   ├── sidepanel/     # 侧边栏 UI
│   │   │   ├── index.html
│   │   │   ├── index.js   # (或 index.jsx 若用React)
│   │   │   └── styles.css
│   │   ├── options/       # 选项页 UI
│   │   │   ├── index.html
│   │   │   ├── index.js
│   │   │   └── styles.css
│   │   └── popup/         # (备选) 弹窗 UI
│   │       ├── index.html
│   │       ├── index.js
│   │       └── styles.css
│   ├── utils/             # 通用工具函数
│   │   ├── storage.js     # 封装 chrome.storage
│   │   └── helpers.js
│   └── assets/            # 静态资源
│       ├── icons/         # 扩展图标 (16, 32, 48, 128)
│       │   ├── icon16.png
│       │   └── ...
│       └── fonts/         # (若需要) 自定义字体
├── manifest.json          # 扩展配置文件 (核心)
├── package.json           # 项目依赖与脚本 (若使用npm)
├── README.md              # 项目说明 (根目录，简要版)
└── .gitignore             # Git 忽略配置
```

## 文件命名规则 (File Naming Conventions)

(来自 系统架构设计.md)

*   **目录 (Folders):** 使用 `kebab-case` (短横线连接的小写字母)，例如 `background`, `content-scripts`, `ui-components`。
*   **JavaScript/TypeScript 文件:**
    *   普通脚本、模块、工具函数: 使用 `camelCase.js` (驼峰命名)，例如 `ttsManager.js`, `pageParser.js`, `utilsHelper.js`。
    *   UI 组件文件 (若使用 React/Vue 等): 使用 `PascalCase.jsx` 或 `PascalCase.vue` (大驼峰)，例如 `ReaderControls.jsx`, `SettingsForm.vue`。
    *   入口文件: 可使用 `index.js`。
*   **CSS/SCSS 文件:** 使用 `kebab-case.css` 或 `styles.css`，例如 `sidepanel-styles.css`。
*   **HTML 文件:** 使用 `kebab-case.html` 或 `index.html`，例如 `options-page.html`。
*   **类 (Classes):** 使用 `PascalCase`。
*   **函数 (Functions) 和变量 (Variables):** 使用 `camelCase`。
*   **常量 (Constants):** 使用 `UPPER_SNAKE_CASE` (全大写下划线连接)，例如 `DEFAULT_SPEED`, `API_ENDPOINT`。

**建议:**

*   **技术选型:** 建议使用 TypeScript 以获得更好的类型检查和代码提示，尤其对于中大型项目。考虑使用 Webpack 或 Vite 等现代构建工具来处理模块打包、代码转换 (TS/JSX) 和资源管理。
*   **一致性:** 最重要的是在整个项目中保持命名和结构风格的一致性。

## 技术方案参考

(来自 阅读插件设计方案.md)

### 前端实现
```javascript
// 示例：章节自动切换检测逻辑
document.addEventListener('DOMContentLoaded', () => {
  const observer = new MutationObserver(checkChapterEnd);
  observer.observe(contentArea, {childList: true});
});

function checkChapterEnd() {
  if (isEndOfChapter()) {
    loadNextChapter().then(triggerAutoRead);
  }
}
```

### 后端服务
*   使用[esbuild构建工具](https://github.com/bingxl/weread)加速开发
*   部署选项：
    *   浏览器本地运行
    *   Docker容器部署（参考Railway方案）
    *   云函数托管

## 🗺️ 开发路线图

### 当前状态 (V0.49) ✅
**阶段 1: MVP - 阅读核心 (已完成)**
- ✅ 插件框架搭建 (Manifest V3)
- ✅ 网站内容解析引擎 (Readability集成)
- ✅ 自动翻页/续读逻辑
- ✅ 基础朗读控制 (播放/暂停/语速)
- ✅ 系统TTS集成
- ✅ 语音选择和管理
- ✅ URL播放功能
- ✅ 播放历史管理

### 近期计划 (V0.5x)
**阶段 2: 朗读体验增强 (进行中)**
- 🔄 第三方云TTS集成 (Azure, Google Cloud)
- 🔄 本地TTS模型支持 (LM Studio)
- 🔄 语音角色识别和分配
- 🔄 阅读数据统计和分析
- 🔄 跨设备设置同步

### 中期目标 (V0.6x-V0.8x)
**阶段 3: AI 理解力注入**
- 🔮 AI智能摘要生成
- 🔮 动态WIKI构建 (实体识别与存储)
- 🔮 AI多角色朗读 V2
- 🔮 内容理解和问答
- 🔮 阅读进度智能管理

### 长期愿景 (V1.0+)
**阶段 4: 可视化与互动升级**
- 🔮 WIKI可视化 (关系图谱、时间线)
- 🔮 AI互动问答系统
- 🔮 剧情预测与竞猜
- 🔮 情感化朗读

**阶段 5: 专业化与平台化**
- 🔮 作者辅助工具集
- 🔮 跨设备同步与云服务
- 🔮 个性化推荐系统
- 🔮 PWA应用支持
- 🔮 社区功能

(来自 阅读插件设计方案.md)

| 阶段 | 内容 | 周期 |
|------|------|------|
| 1 | 核心阅读链路开发 | 2周 |
| 2 | TTS引擎集成 | 1周 |
| 3 | 设置面板开发 | 3天 |
| 4 | 性能优化 | 4天 |

## 扩展可能性

(来自 阅读插件设计方案.md)

1. 增加[PWA应用形态](https://github.com/guozhigq/ReadAloud)支持离线阅读
2. 开发阅读数据统计看板
3. 实现跨设备同步阅读进度

> 注：所有第三方集成均需遵守相关API的使用条款

该设计方案综合了搜索结果的多个技术要点，包含：
1. 内容提取技术（Hansimov/airead）
2. TTS转发架构（qiankun21/ms-ra-forwarder） 
3. 排版优化建议（xiaolai/apple-computer-literacy）
4. 构建工具选择（bingxl/weread）
5. PWA应用参考（guozhigq/ReadAloud）

