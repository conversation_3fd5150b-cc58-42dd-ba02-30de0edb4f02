# 神灯AI·灵阅 - 开发指南

## 开发环境搭建

(待补充，例如：需要的软件、环境配置步骤等)

## 目录结构 (Directory Structure)

(来自 系统架构设计.md)
```
神灯AI朗读增强助手/
├── docs/                  # 项目文档 (重构后)
│   ├── README.md
│   ├── REQUIREMENTS.md
│   ├── DESIGN.md
│   ├── DEVELOPMENT.md
│   ├── FEATURES.md
│   └── CHANGELOG.md
├── dist/                  # 打包后的扩展文件 (构建生成, Git忽略)
├── scripts/               # 构建、部署等辅助脚本
│   └── build.js           # (示例)
├── src/                   # 源代码
│   ├── background/        # Service Worker 相关代码
│   │   └── index.js       # Service Worker 入口
│   ├── content/           # Content Scripts 相关代码
│   │   ├── parser.js      # 页面内容解析逻辑
│   │   └── injector.js    # 注入到页面的主脚本
│   ├── core/              # 核心功能模块 (或称 lib/modules)
│   │   ├── tts-manager.js
│   │   ├── llm-manager.js
│   │   ├── state-manager.js
│   │   ├── wiki-builder.js
│   │   └── role-recognizer.js
│   ├── ui/                # 用户界面相关代码
│   │   ├── components/    # (若使用框架) 可复用UI组件
│   │   │   └── PlayerControls.jsx # (示例)
│   │   ├── sidepanel/     # 侧边栏 UI
│   │   │   ├── index.html
│   │   │   ├── index.js   # (或 index.jsx 若用React)
│   │   │   └── styles.css
│   │   ├── options/       # 选项页 UI
│   │   │   ├── index.html
│   │   │   ├── index.js
│   │   │   └── styles.css
│   │   └── popup/         # (备选) 弹窗 UI
│   │       ├── index.html
│   │       ├── index.js
│   │       └── styles.css
│   ├── utils/             # 通用工具函数
│   │   ├── storage.js     # 封装 chrome.storage
│   │   └── helpers.js
│   └── assets/            # 静态资源
│       ├── icons/         # 扩展图标 (16, 32, 48, 128)
│       │   ├── icon16.png
│       │   └── ...
│       └── fonts/         # (若需要) 自定义字体
├── manifest.json          # 扩展配置文件 (核心)
├── package.json           # 项目依赖与脚本 (若使用npm)
├── README.md              # 项目说明 (根目录，简要版)
└── .gitignore             # Git 忽略配置
```

## 文件命名规则 (File Naming Conventions)

(来自 系统架构设计.md)

*   **目录 (Folders):** 使用 `kebab-case` (短横线连接的小写字母)，例如 `background`, `content-scripts`, `ui-components`。
*   **JavaScript/TypeScript 文件:**
    *   普通脚本、模块、工具函数: 使用 `camelCase.js` (驼峰命名)，例如 `ttsManager.js`, `pageParser.js`, `utilsHelper.js`。
    *   UI 组件文件 (若使用 React/Vue 等): 使用 `PascalCase.jsx` 或 `PascalCase.vue` (大驼峰)，例如 `ReaderControls.jsx`, `SettingsForm.vue`。
    *   入口文件: 可使用 `index.js`。
*   **CSS/SCSS 文件:** 使用 `kebab-case.css` 或 `styles.css`，例如 `sidepanel-styles.css`。
*   **HTML 文件:** 使用 `kebab-case.html` 或 `index.html`，例如 `options-page.html`。
*   **类 (Classes):** 使用 `PascalCase`。
*   **函数 (Functions) 和变量 (Variables):** 使用 `camelCase`。
*   **常量 (Constants):** 使用 `UPPER_SNAKE_CASE` (全大写下划线连接)，例如 `DEFAULT_SPEED`, `API_ENDPOINT`。

**建议:**

*   **技术选型:** 建议使用 TypeScript 以获得更好的类型检查和代码提示，尤其对于中大型项目。考虑使用 Webpack 或 Vite 等现代构建工具来处理模块打包、代码转换 (TS/JSX) 和资源管理。
*   **一致性:** 最重要的是在整个项目中保持命名和结构风格的一致性。

## 技术方案参考

(来自 阅读插件设计方案.md)

### 前端实现
```javascript
// 示例：章节自动切换检测逻辑
document.addEventListener('DOMContentLoaded', () => {
  const observer = new MutationObserver(checkChapterEnd);
  observer.observe(contentArea, {childList: true});
});

function checkChapterEnd() {
  if (isEndOfChapter()) {
    loadNextChapter().then(triggerAutoRead);
  }
}
```

### 后端服务
*   使用[esbuild构建工具](https://github.com/bingxl/weread)加速开发
*   部署选项：
    *   浏览器本地运行
    *   Docker容器部署（参考Railway方案）
    *   云函数托管

## 开发路线图 (High Level Roadmap)

(来自 功能说明.md)

原则: 敏捷开发，快速迭代，优先核心体验，逐步扩展 AI 能力。

**阶段 1: MVP - 阅读核心打磨 (预计 4-6 周)**
*   目标: 实现稳定可靠的基础连续阅读和朗读。
*   关键任务: 插件框架搭建、网站内容解析引擎 (兼容主流网站)、自动翻页/续读逻辑、基础朗读控制、集成系统 TTS、简单语音选择。

**阶段 2: 朗读体验增强 (预计 4-8 周)**
*   目标: 提升朗读质量和灵活性。
*   关键任务: 集成第三方云 TTS、支持本地模型接口、初步的对话/旁白区分与不同声音播放、完善的语音管理、阅读控制功能。

**阶段 3: AI 理解力注入 (预计 8-12 周)**
*   目标: 上线核心 AI 分析功能，构建差异化。
*   关键任务: AI 智能摘要、动态 WIKI (实体识别与存储、基础查询)、AI 多角色朗读 V2 (角色识别与基础声音匹配)。

**阶段 4: 可视化与互动升级 (持续迭代)**
*   目标: 增强信息呈现和用户参与度。
*   关键任务: WIKI 可视化 (关系图谱、时间线)、AI 互动问答、(可选) 剧情预测与竞猜、情感化朗读。

**阶段 5: 专业化与平台化 (长期)**
*   目标: 服务作者用户，完善生态。
*   关键任务: 作者辅助工具集、跨设备同步与云服务、个性化推荐、(可选) 社区建设、PWA。

（注：开发周期为初步估算，具体需根据团队资源和技术复杂度调整。每个阶段都应包含测试和用户反馈环节。）

(来自 阅读插件设计方案.md)

| 阶段 | 内容 | 周期 |
|------|------|------|
| 1 | 核心阅读链路开发 | 2周 |
| 2 | TTS引擎集成 | 1周 |
| 3 | 设置面板开发 | 3天 |
| 4 | 性能优化 | 4天 |

## 扩展可能性

(来自 阅读插件设计方案.md)

1. 增加[PWA应用形态](https://github.com/guozhigq/ReadAloud)支持离线阅读
2. 开发阅读数据统计看板
3. 实现跨设备同步阅读进度

> 注：所有第三方集成均需遵守相关API的使用条款

该设计方案综合了搜索结果的多个技术要点，包含：
1. 内容提取技术（Hansimov/airead）
2. TTS转发架构（qiankun21/ms-ra-forwarder） 
3. 排版优化建议（xiaolai/apple-computer-literacy）
4. 构建工具选择（bingxl/weread）
5. PWA应用参考（guozhigq/ReadAloud）

