> 本文档描述了项目的技术设计方案，包括系统架构、关键模块设计、UI/UX设计原则、技术选型以及重要的设计决策。

# 神灯AI·灵阅 V0.49 - 设计文档

## 🏗️ 系统架构 (基于 Manifest V3)

### 架构概览
神灯AI·灵阅采用现代浏览器扩展架构，基于Manifest V3标准构建，确保安全性、性能和兼容性。

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户界面      │    │   Service       │    │   内容脚本      │
│   (Side Panel)  │◄──►│   Worker        │◄──►│   (Content)     │
│                 │    │   (Background)  │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Chrome APIs   │    │   Storage API   │    │   Page DOM      │
│   (TTS, Tabs)   │    │   (Settings)    │    │   (Content)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 系统架构 (System Architecture) - 基于 Manifest V3

我们将采用现代浏览器扩展的标准架构 (Manifest V3)，主要包含以下几个部分：

### 核心组件详解

#### 1. Service Worker (后台脚本)
**文件**: `src/background/index.js` (3600+行)

**核心职责**:
- 🎵 **TTS播放控制**: 管理语音合成、播放状态
- 📊 **状态管理**: 维护播放状态、用户设置
- 🔄 **连续播放**: 自动章节切换逻辑
- 📡 **消息处理**: 与UI和内容脚本通信
- 🌐 **URL播放**: 后台标签页管理和内容加载

**关键功能**:
```javascript
// 播放控制
function speakText(text, tabId) { /* TTS播放逻辑 */ }
function navigateToNextChapter() { /* 自动翻页逻辑 */ }

// 状态管理
function sendStateUpdate() { /* 状态同步 */ }
function savePlaybackState() { /* 设置持久化 */ }
```

#### 2. 用户界面 (侧边栏)
**文件**: `src/ui/sidepanel/index.js` (4400+行)

**核心职责**:
- 🎮 **播放控制**: 播放/暂停/停止按钮
- ⚙️ **设置管理**: 语音选择、语速调节
- 📝 **URL输入**: 网址播放功能
- 📚 **历史管理**: 播放历史记录
- 📊 **状态显示**: 实时播放状态和进度

**界面特色**:
- 紧凑型设计，不干扰阅读
- 实时状态反馈
- 智能输入控制 (播放时锁定)
- 响应式布局

#### 3. 内容脚本 (页面解析)
**文件**: `src/content/parser.js`

**核心职责**:
- 📄 **内容提取**: 使用Readability库提取主要内容
- 🔗 **链接识别**: 查找"下一页"/"下一章"链接
- 🏷️ **元数据提取**: 获取标题、作者等信息
- 📡 **消息通信**: 与后台脚本数据交换

**解析策略**:
```javascript
// 多策略链接识别
const keywords = ['下一页', '下一章', 'next page', 'next chapter'];
const patterns = [/第\s*(\d+)\s*章/, /chapter\s*(\d+)/i];
```

**Storage:**
*   **职责:** 持久化存储用户设置、阅读进度、收藏的语音、API Keys (安全存储) 等。
*   **技术:** 使用 `chrome.storage.local` (本地存储) 或 `chrome.storage.sync` (跨设备同步，有容量限制)。敏感信息（如 API Key）需要额外加密处理或通过更安全的方式管理。

**通讯机制:**
*   各部分之间通过 `chrome.runtime.sendMessage` 和 `chrome.tabs.sendMessage` 以及相关的监听器 (`onMessage`) 进行通信。

**核心逻辑/模块 (Core Logic / Modules):**
*   **职责:** 封装可重用的核心功能，如：
    *   `PageParser`: 网页内容解析与下一页/章识别逻辑。
    *   `TTSManager`: 抽象不同 TTS 引擎（系统、云、本地）的接口，管理语音列表和播放。
    *   `LLMManager`: 抽象与 LLM API 或本地模型的交互，处理摘要、WIKI 构建、问答、预测等任务。
    *   `StateManager`: 管理应用的核心状态（阅读状态、设置等）。
    *   `WikiBuilder`: 负责从文本构建和查询 WIKI 数据。
    *   `RoleRecognizer`: 负责识别对话角色。
*   **技术:** JavaScript/TypeScript。这些模块会被 Service Worker、UI 或 Content Scripts 按需调用。

## UI 设计方案

**(来自 UI设计建议.md)**

Okay, let's push the boundaries and predict a design scheme that aims to be **trendsetting and distinctive**, drawing inspiration from successful (and sometimes niche) web/graphic design directions of the past ~4 years (roughly 2020-2024) and projecting forward.

The core idea is to blend **sophistication with a touch of the unexpected**, moving beyond standard dark mode or simple vibrant accents. We'll incorporate elements like subtle textural hints, refined gradients, and perhaps an unconventional accent color, aiming for an **"Experiential Ambient"** feel.

**Concept: Experiential Ambient**

*   **Inspiration:** Subtle Aurora UI/Mesh Gradients, Tactile Interfaces (softness beyond Neumorphism), Refined Maximalism (richness without clutter), focus on atmospheric lighting.
*   **Goal:** Create an interface that feels immersive, premium, and slightly ethereal, making interaction feel smooth and engaging, not just functional.

---

### Predicted Trendsetting Design Scheme: "Experiential Ambient"

*   **Core Idea:** Utilizes a deep, nuanced background with subtle color shifts, frosted glass/translucent layers for containers, and a sophisticated, perhaps unexpected, primary accent color. Focus on smooth transitions and subtle interactive feedback (like glows).

*   **配色详情 (Color Palette Details):**

    *   `--background-primary`: **Subtle Mesh Gradient.** Instead of a flat color, imagine a *very dark*, slow-moving mesh gradient background using deep, desaturated tones like:
        *   `#10121A` (Deep Midnight Blue)
        *   `#181528` (Dark Violet Dusk)
        *   `#121A18` (Deep Forest Green/Teal hint)
        *   *Effect:* Provides depth and a sense of atmosphere without being distracting. Barely perceptible shifts.

    *   `--surface-container`: **Frosted Glass/Translucent.** Apply a Glassmorphism effect to the main containers (like the player and settings area).
        *   `background-color`: `rgba(35, 32, 55, 0.6)` (Semi-transparent, slightly cool/purple dark base)
        *   `backdrop-filter`: `blur(18px)`
        *   `border`: `1px solid rgba(255, 255, 255, 0.1)` (Subtle edge highlight)
        *   *Fallback (if transparency isn't desired/performant):* `#232037` (Solid dark violet-grey)
        *   *Effect:* Layers content elegantly, allows the subtle background ambiance to peek through, feels modern and light despite the dark theme.

    *   `--primary-accent`: **Sophisticated & Unexpected.** Choose a color that stands out but isn't the standard tech blue/green. Examples:
        *   `#E1AD5B` (**Refined Ochre/Gold:** A warmer, more muted, sophisticated take on the original gold)
        *   OR `#C882D1` (**Dusty Rose/Mauve:** Softer, elegant, less common in tech UI)
        *   OR `#5B E1AD` (**Muted Teal/Jade:** A calmer, more natural green tone)
        *   *Let's choose Refined Ochre for this example:* `#E1AD5B`
        *   *Effect:* Provides a unique signature, feels premium and warm against the cool, dark background.

    *   `--primary-accent-interaction`: **Subtle Glow/Light.** For hover/active states, instead of just changing color, add a subtle outer glow or slight increase in saturation/brightness.
        *   `color`: `#EECB86` (Slightly brighter Ochre)
        *   `box-shadow` (Example for button hover): `0 0 15px rgba(225, 173, 91, 0.4)` (Soft Ochre glow)
        *   *Effect:* Enhances the "ambient light" feel, makes interactions feel responsive and alive.

    *   `--text-primary`: `#F0F0F5` (Soft Off-White - avoid pure white for better blending).
    *   `--text-secondary`: `#A09CB0` (Muted Lavender Grey - complements the background and provides soft contrast).
    *   `--slider-track`: `#3A364F` (Dark, slightly desaturated violet-grey). The slider *thumb* would use `--primary-accent`.
    *   `--dropdown-bg`: Uses the `--surface-container` style (Glassmorphism or solid fallback).
    *   `--dropdown-border`: `1px solid rgba(225, 173, 91, 0.5)` (Subtle accent border on focus/open).

*   **字体 (Typography):**
    *   Use a modern, highly readable sans-serif (like Inter, Satoshi, or Manrope) for body text and labels.
    *   Consider a slightly more characterful (but still clean) sans-serif or even a *very* refined serif for large headings if applicable elsewhere in the app (not shown here), to add personality.

*   **图标 (Iconography):**
    *   Use a clean, consistent icon set (e.g., Phosphor Icons, Feather Icons).
    *   Primary interactive icons (Play) use `--primary-accent`.
    *   Secondary icons/text use `--text-secondary`, changing to `--text-primary` or `--primary-accent` on interaction/selection.

*   **关键理念 (Key Philosophy):**
    1.  **Atmosphere over Flatness:** Use subtle gradients and translucency to create depth.
    2.  **Sophisticated Accent:** Move beyond predictable primary colors.
    3.  **Tactile Feedback:** Use glows and smooth transitions for interactions.
    4.  **Readability First:** Ensure text contrast remains high despite atmospheric effects.
    5.  **Cohesion:** All elements should feel like they belong to the same refined, ambient world.

---

**Why this might be trendsetting:**

*   **Evolves Dark Mode:** Moves beyond simple flat dark themes by adding depth and texture (gradient, glass).
*   **Integrates Emerging Trends:** Leverages Mesh Gradients and Glassmorphism in a functional UI context.
*   **Focuses on "Feel":** Aims for a more experiential interaction through subtle lighting effects and sophisticated color choices.
*   **Unique Accent:** The choice of a less common accent color makes it immediately distinctive.

This is a prediction, of course, and requires careful implementation (especially performance with gradients and blur) and thorough accessibility testing (contrast ratios with transparency). But it aims for that unique, forward-looking aesthetic you requested.

**(来自 阅读插件设计方案.md)**
```css
/* 阅读界面优化（参考[github.com/xiaolai/apple-computer-literacy](https://github.com/xiaolai/apple-computer-literacy/blob/main/ms-edge.md)的排版建议） */
.reader-container {
  font-size: 18px;
  letter-spacing: 0.05em;
  line-height: 1.8;
}
```

## 语音设置页面详细设计

### 1. 页面布局与结构

语音设置页面采用简洁、直观的卡片式布局，专注于语音选择与管理功能，结构如下：

#### 1.1 顶部标题区域
```
┌────────────────────────────────────────────────────┐
│  语音设置                                       ×  │
└────────────────────────────────────────────────────┘
```

- 标题使用 `--text-primary` 颜色
- 关闭按钮添加微妙的悬停效果，使用 `--primary-accent-interaction` 作为悬停状态

#### 1.2 搜索与筛选区域（第一个卡片）
```
┌────────────────────────────────────────────────────┐
│  ┌─搜索语音───────────────────────────────────┐    │
│  └───────────────────────────────────────────┘    │
│                                                    │
│  [全部] [中文] [英文] [日文] [其他]                │
│                                                    │
│  [男声] [女声]         [方言 ▼]                    │
└────────────────────────────────────────────────────┘
```

- 使用 `--surface-container` 样式，实现磨砂玻璃效果
- 搜索框使用细微的内部阴影和轻微的边框高亮
- 筛选标签采用胶囊形状，选中状态使用 `--primary-accent` 背景色，非选中状态使用半透明暗色背景
- 标签切换时添加平滑的颜色过渡动画 (0.2s)
- 方言下拉菜单在展开时使用微妙的阴影效果

#### 1.3 语音列表区域（第二个卡片）
```
┌────────────────────────────────────────────────────┐
│  所有语音                                          │
│  ┌────────────────────────────────────────────┐   │
│  │  🔝 [默认语音]         [试听] [★] [默认✓]   │   │
│  │  语言: 中文  性别: 女声  方言: 普通话      │   │
│  │                                            │   │
│  │  🔝 [收藏语音1]        [试听] [★] [默认]    │   │
│  │  语言: 英文  性别: 男声  方言: 美式        │   │
│  │                                            │   │
│  │  [普通语音1]          [试听] [☆] [默认]    │   │
│  │  语言: 中文  性别: 男声                    │   │
│  │                                            │   │
│  └────────────────────────────────────────────┘   │
└────────────────────────────────────────────────────┘
```

- 同样使用 `--surface-container` 样式
- 默认语音和收藏语音项使用微妙的背景色差异 (rgba(225, 173, 91, 0.1)) 突出显示
- 语音条目采用双行设计：上行为语音名称和功能按钮，下行为语音特性标签
- 功能按钮使用直观的图标：
  - 试听：🔊 (播放时变为动态波形图标)
  - 收藏：☆/★ (未收藏/已收藏)
  - 默认：○/✓ (未设为默认/已设为默认)
- 按钮悬停时有微妙的光晕效果

#### 1.4 AI语音区域（底部区域）
```
┌────────────────────────────────────────────────────┐
│  AI语音设置                      [即将推出]        │
│  ┌────────────────────────────────────────────┐   │
│  │                                            │   │
│  │     神灯AI语音功能即将推出，敬请期待！     │   │
│  │                                            │   │
│  └────────────────────────────────────────────┘   │
└────────────────────────────────────────────────────┘
```

- 使用略微不同的背景色区分
- "即将推出"标签使用醒目但不刺眼的对比色

#### 1.5 底部按钮区域
```
┌────────────────────────────────────────────────────┐
│                                   [保存设置]       │
└────────────────────────────────────────────────────┘
```

- 保存按钮使用 `--primary-accent` 作为背景色
- 按钮悬停时应用 `--primary-accent-interaction` 效果

### 2. 交互细节设计

#### 2.1 状态变更动画
- **语音收藏/取消收藏**:
  - 收藏时，语音条目以平滑的动画(0.3s)提升至收藏区域
  - 取消收藏时，以淡出(0.2s)动画移除
- **设为默认**:
  - 设为默认时，原默认语音的"默认"状态以淡出效果(0.2s)消失
  - 新默认语音的"默认"按钮以轻微的放大(scale: 1.05)和发光动画强调变化

#### 2.2 试听状态指示
- 试听按钮点击后转变为动态音频波形图标
- 试听过程中语音条目左侧添加细微的脉动边框效果
- 试听结束时平滑过渡回静态状态

#### 2.3 空状态设计
- **首次使用引导**:
  ```
  ┌────────────────────────────────────────────────────┐
  │  尚未添加收藏语音                                  │
  │  点击语音列表中的 ☆ 将语音添加到收藏              │
  │  收藏的语音将显示在侧边栏中方便快速选择           │
  └────────────────────────────────────────────────────┘
  ```
  - 使用柔和但醒目的提示框
  - 添加微妙的指向动画引导用户视线
  
- **搜索无结果**:
  ```
  ┌────────────────────────────────────────────────────┐
  │  未找到匹配的语音                                  │
  │  • 尝试其他关键词                                  │
  │  • 减少筛选条件                                    │
  │  • 检查是否有拼写错误                              │
  └────────────────────────────────────────────────────┘
  ```
  - 提供清晰的建议选项
  - 使用微妙的淡入效果显示

### 3. 视觉细节调整

#### 3.1 语音类型标识
- **语言标识**:
  - 每种语言使用微小的国旗或语言代码图标
  - 中文: 🇨🇳 / zh
  - 英文: 🇬🇧 / en
  - 日文: 🇯🇵 / ja
  - 其他语言使用对应的国旗/代码

#### 3.2 收藏与默认语音视觉分组
- 使用浅色分隔线将置顶的默认/收藏语音与普通语音分开
- 分隔线使用 `rgba(225, 173, 91, 0.2)` 颜色，与主题色相呼应
- 默认和收藏语音区域添加微妙的左侧边框高亮 (2px)

#### 3.3 语音条目视觉层次
- 语音名称使用 `--text-primary` 颜色和稍大的字号 (16px)
- 语音特性标签使用 `--text-secondary` 颜色和较小字号 (12px)
- 功能按钮在静默状态下使用浅色，激活状态使用 `--primary-accent`
- 已收藏和默认语音使用更饱和的色彩，增强视觉区分度

### 4. 技术实现要点

#### 4.1 语音列表渲染
```javascript
function renderVoicesList(voices) {
  // 首先对语音进行排序：默认语音 > 收藏语音 > 其他语音
  const sortedVoices = sortVoicesByPriority(voices);
  
  // 清空当前列表
  voicesListContainer.innerHTML = '';
  
  // 检查是否为空状态
  if (sortedVoices.length === 0) {
    renderEmptyState();
    return;
  }
  
  // 渲染语音条目
  let lastWasPriority = false; // 跟踪是否需要添加分隔线
  
  sortedVoices.forEach((voice, index) => {
    const isPriority = voice.isDefault || voice.isFavorite;
    
    // 在普通语音和优先语音之间添加分隔线
    if (lastWasPriority && !isPriority) {
      addSeparator();
    }
    
    renderVoiceItem(voice);
    lastWasPriority = isPriority;
  });
  
  // 添加交互事件监听器
  addVoiceItemEventListeners();
}
```

#### 4.2 状态变更动画实现
```javascript
function toggleFavorite(voiceName) {
  const voiceItem = document.querySelector(`.voice-item[data-voice-name="${voiceName}"]`);
  const isFavorite = favoriteVoices.includes(voiceName);
  
  if (!isFavorite) {
    // 添加到收藏
    favoriteVoices.push(voiceName);
    
    // 动画效果
    voiceItem.classList.add('animating');
    voiceItem.style.transition = 'transform 0.3s, opacity 0.3s';
    
    // 更新UI
    updateVoiceItemUI(voiceItem, true);
    
    // 动画完成后重新渲染列表
    setTimeout(() => {
      voiceItem.classList.remove('animating');
      renderVoicesList(availableVoices);
    }, 300);
  } else {
    // 从收藏中移除
    const index = favoriteVoices.indexOf(voiceName);
    favoriteVoices.splice(index, 1);
    
    // 淡出动画
    voiceItem.style.transition = 'opacity 0.2s';
    voiceItem.style.opacity = '0.5';
    
    // 动画完成后重新渲染列表
    setTimeout(() => {
      renderVoicesList(availableVoices);
    }, 200);
  }
  
  // 保存收藏
  saveFavoriteVoices();
}
```

#### 4.3 试听功能实现
```javascript
function previewVoice(voiceName) {
  const voiceItem = document.querySelector(`.voice-item[data-voice-name="${voiceName}"]`);
  const previewBtn = voiceItem.querySelector('.preview-btn');
  
  // 更新UI为播放状态
  previewBtn.innerHTML = '<div class="audio-wave"></div>';
  voiceItem.classList.add('previewing');
  
  // 获取语言相关的示例文本
  const voice = availableVoices.find(v => v.voiceName === voiceName);
  const sampleText = getSampleTextForLanguage(voice.lang);
  
  // 播放语音
  chrome.tts.speak(
    sampleText,
    {
      voiceName: voiceName,
      rate: 1.0,
      onEvent: function(event) {
        if (event.type === 'end' || event.type === 'interrupted' || event.type === 'error') {
          // 恢复UI
          previewBtn.innerHTML = '🔊';
          voiceItem.classList.remove('previewing');
        }
      }
    }
  );
}
```

### 5. 侧边栏集成

语音设置页面的变更将与侧边栏的语音控制同步，确保一致的用户体验：

```javascript
function syncWithSidebar() {
  // 将默认和收藏语音同步到侧边栏下拉框
  chrome.runtime.sendMessage({
    action: 'updateVoiceOptions',
    defaultVoice: defaultVoice,
    favoriteVoices: favoriteVoices
  });
}
```

这确保用户在语音设置页面中做出的选择能够立即反映在侧边栏界面中，提供无缝的用户体验。

### 6. 后续扩展考虑

虽然当前设计专注于基础语音管理功能，但系统为未来功能扩展做好了准备：

1. **语音分组**：未来可添加用户自定义语音分组功能
2. **AI语音训练**：为即将推出的AI语音模块预留了界面位置
3. **高级筛选**：可扩展支持更多筛选条件，如音质评级、使用频率等

## 技术选型 (Technology Choices)

// ... existing code ...