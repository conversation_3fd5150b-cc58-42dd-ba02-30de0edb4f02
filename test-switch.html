<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开关测试</title>
    <link rel="stylesheet" href="src/ui/sidepanel/compact-styles.css">
    <style>
        body {
            background: #1a1a2e;
            color: #F0F0F5;
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
        }
        .test-item {
            margin: 20px 0;
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 5px;
            border: 1px dashed rgba(255, 255, 255, 0.2);
            border-radius: 4px;
        }
        .test-label {
            font-size: 14px;
            min-width: 120px;
        }
        /* 添加高度参考线 */
        .test-item::after {
            content: '';
            position: absolute;
            right: 10px;
            width: 2px;
            height: 36px;
            background: rgba(225, 173, 91, 0.3);
            border-radius: 1px;
        }
        .test-item {
            position: relative;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>UI元素高度统一测试</h2>

        <!-- 四个元素高度测试 -->
        <div class="test-item">
            <span class="test-label">时间显示:</span>
            <div id="playback-timer" class="playback-timer">00:00:00</div>
        </div>

        <div class="test-item">
            <span class="test-label">播放按钮:</span>
            <button id="play-pause-btn" class="control-btn main-btn" title="播放">
                <span class="icon-play">▶</span>
            </button>
        </div>

        <div class="test-item">
            <span class="test-label">停止按钮:</span>
            <button id="stop-btn" class="control-btn small-btn" title="停止">⏹</button>
        </div>

        <div class="test-item">
            <span class="test-label">状态显示:</span>
            <div id="status-indicator" class="status-indicator idle">空闲</div>
        </div>

        <hr style="margin: 20px 0; border-color: rgba(255,255,255,0.2);">

        <!-- 开关测试 -->
        <h3>连续播放开关测试</h3>

        <div class="test-item">
            <span class="test-label">开关 (关闭):</span>
            <label class="switch">
                <input type="checkbox" id="test-toggle-1">
                <span class="slider round"></span>
            </label>
        </div>

        <div class="test-item">
            <span class="test-label">开关 (开启):</span>
            <label class="switch">
                <input type="checkbox" id="test-toggle-2" checked>
                <span class="slider round"></span>
            </label>
        </div>
        
        <p style="margin-top: 30px; font-size: 12px; color: rgba(255, 255, 255, 0.6);">
            <strong>高度统一修正：</strong><br>
            • 时间显示、播放按钮、停止按钮、状态显示：统一高度36px<br>
            • 使用flex布局垂直居中对齐<br>
            • 响应式设计下统一高度32px<br><br>
            <strong>开关修正：</strong><br>
            • 开关高度：26px（足够容纳20px圆圈）<br>
            • 黄色背景高度：26px（完全包含圆圈）<br>
            • 圆圈垂直居中定位<br>
            • 圆角半径：13px（高度的一半）
        </p>
    </div>
    
    <script>
        // 添加点击事件测试
        document.getElementById('test-toggle-3').addEventListener('change', function() {
            console.log('开关状态:', this.checked ? '开启' : '关闭');
        });
    </script>
</body>
</html>
