<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开关测试</title>
    <link rel="stylesheet" href="src/ui/sidepanel/compact-styles.css">
    <style>
        body {
            background: #1a1a2e;
            color: #F0F0F5;
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
        }
        .test-item {
            margin: 20px 0;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .test-label {
            font-size: 14px;
            min-width: 120px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>连续播放开关测试</h2>
        
        <div class="test-item">
            <span class="test-label">开关 (关闭):</span>
            <label class="switch">
                <input type="checkbox" id="test-toggle-1">
                <span class="slider round"></span>
            </label>
        </div>
        
        <div class="test-item">
            <span class="test-label">开关 (开启):</span>
            <label class="switch">
                <input type="checkbox" id="test-toggle-2" checked>
                <span class="slider round"></span>
            </label>
        </div>
        
        <div class="test-item">
            <span class="test-label">可点击测试:</span>
            <label class="switch">
                <input type="checkbox" id="test-toggle-3">
                <span class="slider round"></span>
            </label>
        </div>
        
        <p style="margin-top: 30px; font-size: 12px; color: rgba(255, 255, 255, 0.6);">
            点击开关测试交互效果。检查圆圈是否完全包含在背景椭圆形内。<br>
            <strong>修正内容：</strong><br>
            • 开关高度：26px（足够容纳20px圆圈）<br>
            • 黄色背景高度：26px（完全包含圆圈）<br>
            • 圆圈垂直居中定位<br>
            • 圆角半径：13px（高度的一半）
        </p>
    </div>
    
    <script>
        // 添加点击事件测试
        document.getElementById('test-toggle-3').addEventListener('change', function() {
            console.log('开关状态:', this.checked ? '开启' : '关闭');
        });
    </script>
</body>
</html>
