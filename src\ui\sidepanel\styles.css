/* 神灯AI·灵阅 - 主样式文件 */

/* === 基础样式重置 === */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Aria<PERSON>, sans-serif;
    background: linear-gradient(135deg, #2C2C54 0%, #40407A 50%, #2C2C54 100%);
    color: #E8E8E8;
    overflow-x: hidden;
}

.container {
    min-height: 100vh;
    padding: 8px;
    display: flex;
    flex-direction: column;
}

/* === 头部样式 === */
.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    margin-bottom: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
}

.app-icon {
    width: 24px;
    height: 24px;
}

.logo-text {
    font-size: 14px;
    font-weight: 600;
    color: #FFD700;
    text-shadow: 0 0 8px rgba(255, 215, 0, 0.3);
}

.slogan-symbol {
    color: rgba(255, 215, 0, 0.6);
    font-weight: 400;
}

.settings-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    padding: 6px;
    color: #E8E8E8;
    cursor: pointer;
    transition: all 0.3s ease;
}

.settings-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 215, 0, 0.5);
}

.settings-icon {
    width: 16px;
    height: 16px;
}

/* 荧光蓝滚动条样式 */
::-webkit-scrollbar {
    width: 12px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 6px;
    border: 1px solid rgba(0, 255, 255, 0.2);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, 
        #00ffff, 
        #0080ff, 
        #00ffff, 
        #0080ff
    );
    background-size: 200% 200%;
    border-radius: 6px;
    border: 1px solid rgba(0, 255, 255, 0.5);
    box-shadow: 
        0 0 10px rgba(0, 255, 255, 0.5),
        inset 0 0 10px rgba(0, 255, 255, 0.3);
    animation: scrollbarFlow 3s ease-in-out infinite;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, 
        #00ffff, 
        #0099ff, 
        #00ffff, 
        #0099ff
    );
    box-shadow: 
        0 0 15px rgba(0, 255, 255, 0.8),
        inset 0 0 15px rgba(0, 255, 255, 0.5);
}

/* 滚动条流动光线动画 */
@keyframes scrollbarFlow {
    0% {
        background-position: 0% 50%;
        box-shadow: 
            0 0 10px rgba(0, 255, 255, 0.5),
            inset 0 0 10px rgba(0, 255, 255, 0.3);
    }
    50% {
        background-position: 100% 50%;
        box-shadow: 
            0 0 20px rgba(0, 255, 255, 0.8),
            inset 0 0 20px rgba(0, 255, 255, 0.6);
    }
    100% {
        background-position: 0% 50%;
        box-shadow: 
            0 0 10px rgba(0, 255, 255, 0.5),
            inset 0 0 10px rgba(0, 255, 255, 0.3);
    }
}

/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #2C2C54 0%, #40407A 50%, #2C2C54 100%);
    color: #E8E8E8;
    min-height: 100vh;
    overflow-x: hidden;
}

.container {
    padding: 16px;
    max-width: 100%;
    margin: 0 auto;
}

/* 标题样式 */
.header {
    text-align: center;
    margin-bottom: 20px;
    padding: 16px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.title {
    font-size: 20px;
    font-weight: 600;
    color: #FFD700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
    margin-bottom: 4px;
}

.subtitle {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    font-weight: 400;
}

/* 播放控制区域 */
.playback-controls {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 16px;
    backdrop-filter: blur(10px);
}

/* 播放按钮 */
.play-pause-button {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: none;
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: #2C2C54;
    font-size: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.play-pause-button:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);
}

.play-pause-button.playing {
    background: linear-gradient(135deg, #FF6B6B, #FF8E53);
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% {
        box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
    }
    50% {
        box-shadow: 0 6px 25px rgba(255, 107, 107, 0.6);
    }
}

/* 停止按钮 */
.stop-button {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
    color: #E8E8E8;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stop-button:hover:not(:disabled) {
    background: rgba(255, 107, 107, 0.2);
    border-color: rgba(255, 107, 107, 0.5);
    color: #FF6B6B;
}

.stop-button:disabled {
    opacity: 0.3;
    cursor: not-allowed;
}

/* 状态显示 */
.status-section {
    margin-bottom: 16px;
}

.status-indicator {
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.status-indicator.idle {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.7);
}

.status-indicator.reading {
    background: rgba(255, 215, 0, 0.2);
    color: #FFD700;
    border: 1px solid rgba(255, 215, 0, 0.3);
    animation: readingGlow 2s ease-in-out infinite;
    position: relative;
    overflow: hidden;
}

.status-indicator.paused {
    background: rgba(255, 165, 0, 0.2);
    color: #FFA500;
    border: 1px solid rgba(255, 165, 0, 0.3);
}

@keyframes readingGlow {
    0%, 100% {
        box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
    }
}

/* 朗读中状态的流动银色边框 */
.status-indicator.reading::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, 
        transparent, 
        rgba(192, 192, 192, 0.8), 
        transparent, 
        rgba(192, 192, 192, 0.8), 
        transparent
    );
    background-size: 200% 200%;
    border-radius: 22px;
    z-index: -1;
    animation: silverFlow 3s linear infinite;
}

@keyframes silverFlow {
    0% {
        background-position: 0% 50%;
    }
    100% {
        background-position: 200% 50%;
    }
}

/* 章节信息 */
.chapter-info {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 16px;
}

.chapter-title {
    font-size: 14px;
    font-weight: 500;
    color: #E8E8E8;
    margin-bottom: 4px;
    line-height: 1.4;
}

.chapter-meta {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.5);
}

/* 控制面板 */
.controls-section {
    margin-bottom: 16px;
}

.section-title {
    font-size: 14px;
    font-weight: 500;
    color: #E8E8E8;
    margin-bottom: 12px;
    padding-bottom: 4px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* 语速控制 */
.speed-control {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.speed-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    min-width: 40px;
}

.speed-slider {
    flex: 1;
    height: 4px;
    border-radius: 2px;
    background: rgba(255, 255, 255, 0.2);
    outline: none;
    -webkit-appearance: none;
}

.speed-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #FFD700;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(255, 215, 0, 0.3);
}

.speed-value {
    font-size: 12px;
    color: #FFD700;
    min-width: 30px;
    text-align: right;
}

/* 设置按钮 */
.settings-button {
    width: 100%;
    padding: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
    color: #E8E8E8;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.settings-button:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 215, 0, 0.5);
}

/* 输入框样式 */
.input-group {
    margin-bottom: 16px;
}

.input-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 6px;
    display: block;
}

.input-field {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.05);
    color: #E8E8E8;
    font-size: 14px;
    transition: all 0.3s ease;
}

.input-field:focus {
    outline: none;
    border-color: rgba(255, 215, 0, 0.5);
    background: rgba(255, 255, 255, 0.1);
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.2);
}

.input-field::placeholder {
    color: rgba(255, 255, 255, 0.4);
}

/* 按钮组 */
.button-group {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
}

.button-group .button {
    flex: 1;
    padding: 10px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.05);
    color: #E8E8E8;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.button-group .button:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 215, 0, 0.5);
}

.button-group .button.active {
    background: rgba(255, 215, 0, 0.2);
    border-color: rgba(255, 215, 0, 0.5);
    color: #FFD700;
}

/* 列表样式 */
.list-container {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.02);
}

.list-item {
    padding: 10px 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    cursor: pointer;
    transition: all 0.3s ease;
}

.list-item:last-child {
    border-bottom: none;
}

.list-item:hover {
    background: rgba(255, 255, 255, 0.05);
}

.list-item.selected {
    background: rgba(255, 215, 0, 0.1);
    border-color: rgba(255, 215, 0, 0.3);
}

/* === 主要控制区域 === */
.main-controls {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 12px;
}

.control-buttons-container {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: center;
    margin-bottom: 12px;
}

.main-btn {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    border: none;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    color: #2C2C54;
    font-size: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.main-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 16px rgba(255, 215, 0, 0.5);
}

.main-btn.playing {
    background: linear-gradient(135deg, #FF6B6B, #FF8E53);
    animation: pulse 2s ease-in-out infinite;
}

.small-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    padding: 8px 12px;
    color: #E8E8E8;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.small-btn:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 215, 0, 0.5);
}

.small-btn:disabled {
    opacity: 0.3;
    cursor: not-allowed;
}

/* === 状态显示 === */
.status-section {
    margin-bottom: 12px;
}

.status-indicator {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 8px 16px;
    text-align: center;
    font-size: 12px;
    font-weight: 500;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.status-indicator.reading {
    background: rgba(255, 215, 0, 0.2);
    border-color: rgba(255, 215, 0, 0.5);
    color: #FFD700;
    position: relative;
    overflow: hidden;
    animation: readingGlow 2s ease-in-out infinite;
}

.status-indicator.paused {
    background: rgba(255, 165, 0, 0.2);
    border-color: rgba(255, 165, 0, 0.5);
    color: #FFA500;
}

.status-indicator.idle {
    color: rgba(255, 255, 255, 0.7);
}

/* === 设置覆盖层 === */
.settings-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.settings-content {
    background: linear-gradient(135deg, #2C2C54 0%, #40407A 50%, #2C2C54 100%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 20px;
    max-width: 400px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-header h2 {
    font-size: 16px;
    font-weight: 600;
    color: #FFD700;
}

.close-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 28px;
    height: 28px;
    color: #E8E8E8;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background: rgba(255, 107, 107, 0.2);
    border-color: rgba(255, 107, 107, 0.5);
    color: #FF6B6B;
}

/* === 卡片样式 === */
.card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 12px;
}

.card-header {
    font-size: 14px;
    font-weight: 600;
    color: #E8E8E8;
    margin-bottom: 8px;
    padding-bottom: 4px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* === 输入框样式 === */
.input-group {
    margin-bottom: 12px;
}

.input-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 4px;
    display: block;
}

input, select, textarea {
    width: 100%;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    padding: 8px 12px;
    color: #E8E8E8;
    font-size: 12px;
    transition: all 0.3s ease;
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: rgba(255, 215, 0, 0.5);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 8px rgba(255, 215, 0, 0.2);
}

input::placeholder {
    color: rgba(255, 255, 255, 0.4);
}

/* === 滑块样式 === */
.slider-container {
    margin: 8px 0;
}

.slider-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 4px;
    display: block;
}

.speed-slider, .slider {
    width: 100%;
    height: 4px;
    border-radius: 2px;
    background: rgba(255, 255, 255, 0.2);
    outline: none;
    -webkit-appearance: none;
    cursor: pointer;
}

.speed-slider::-webkit-slider-thumb, .slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #FFD700;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(255, 215, 0, 0.3);
    transition: all 0.3s ease;
}

.speed-slider::-webkit-slider-thumb:hover, .slider::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 3px 8px rgba(255, 215, 0, 0.5);
}

/* === 开关样式 === */
.switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.switch .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.2);
    transition: 0.3s;
    border-radius: 24px;
}

.switch .slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: #E8E8E8;
    transition: 0.3s;
    border-radius: 50%;
}

.switch input:checked + .slider {
    background-color: rgba(255, 215, 0, 0.6);
}

.switch input:checked + .slider:before {
    transform: translateX(20px);
    background-color: #FFD700;
}

/* === 章节信息 === */
.chapter-info {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    padding: 8px;
    margin-bottom: 8px;
}

.chapter-title {
    font-size: 12px;
    font-weight: 500;
    color: #E8E8E8;
    margin-bottom: 4px;
    line-height: 1.3;
}

.chapter-url {
    font-size: 10px;
    color: rgba(255, 255, 255, 0.5);
    word-break: break-all;
}

.chapter-progress {
    font-size: 10px;
    color: rgba(255, 255, 255, 0.6);
    margin-top: 4px;
}

/* 响应式设计 */
@media (max-width: 320px) {
    .container {
        padding: 6px;
    }

    .panel-header {
        padding: 6px 8px;
    }

    .main-btn {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    .control-buttons-container {
        gap: 6px;
    }

    .card {
        padding: 8px;
        margin-bottom: 8px;
    }
}
