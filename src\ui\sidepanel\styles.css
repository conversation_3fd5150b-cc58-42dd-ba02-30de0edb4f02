/* 神灯AI·灵阅 - 主样式文件 */

/* 荧光蓝滚动条样式 */
::-webkit-scrollbar {
    width: 12px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 6px;
    border: 1px solid rgba(0, 255, 255, 0.2);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, 
        #00ffff, 
        #0080ff, 
        #00ffff, 
        #0080ff
    );
    background-size: 200% 200%;
    border-radius: 6px;
    border: 1px solid rgba(0, 255, 255, 0.5);
    box-shadow: 
        0 0 10px rgba(0, 255, 255, 0.5),
        inset 0 0 10px rgba(0, 255, 255, 0.3);
    animation: scrollbarFlow 3s ease-in-out infinite;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, 
        #00ffff, 
        #0099ff, 
        #00ffff, 
        #0099ff
    );
    box-shadow: 
        0 0 15px rgba(0, 255, 255, 0.8),
        inset 0 0 15px rgba(0, 255, 255, 0.5);
}

/* 滚动条流动光线动画 */
@keyframes scrollbarFlow {
    0% {
        background-position: 0% 50%;
        box-shadow: 
            0 0 10px rgba(0, 255, 255, 0.5),
            inset 0 0 10px rgba(0, 255, 255, 0.3);
    }
    50% {
        background-position: 100% 50%;
        box-shadow: 
            0 0 20px rgba(0, 255, 255, 0.8),
            inset 0 0 20px rgba(0, 255, 255, 0.6);
    }
    100% {
        background-position: 0% 50%;
        box-shadow: 
            0 0 10px rgba(0, 255, 255, 0.5),
            inset 0 0 10px rgba(0, 255, 255, 0.3);
    }
}

/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #2C2C54 0%, #40407A 50%, #2C2C54 100%);
    color: #E8E8E8;
    min-height: 100vh;
    overflow-x: hidden;
}

.container {
    padding: 16px;
    max-width: 100%;
    margin: 0 auto;
}

/* 标题样式 */
.header {
    text-align: center;
    margin-bottom: 20px;
    padding: 16px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.title {
    font-size: 20px;
    font-weight: 600;
    color: #FFD700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
    margin-bottom: 4px;
}

.subtitle {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    font-weight: 400;
}

/* 播放控制区域 */
.playback-controls {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 16px;
    backdrop-filter: blur(10px);
}

/* 播放按钮 */
.play-pause-button {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: none;
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: #2C2C54;
    font-size: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.play-pause-button:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);
}

.play-pause-button.playing {
    background: linear-gradient(135deg, #FF6B6B, #FF8E53);
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% {
        box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
    }
    50% {
        box-shadow: 0 6px 25px rgba(255, 107, 107, 0.6);
    }
}

/* 停止按钮 */
.stop-button {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
    color: #E8E8E8;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stop-button:hover:not(:disabled) {
    background: rgba(255, 107, 107, 0.2);
    border-color: rgba(255, 107, 107, 0.5);
    color: #FF6B6B;
}

.stop-button:disabled {
    opacity: 0.3;
    cursor: not-allowed;
}

/* 状态显示 */
.status-section {
    margin-bottom: 16px;
}

.status-indicator {
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.status-indicator.idle {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.7);
}

.status-indicator.reading {
    background: rgba(255, 215, 0, 0.2);
    color: #FFD700;
    border: 1px solid rgba(255, 215, 0, 0.3);
    animation: readingGlow 2s ease-in-out infinite;
    position: relative;
    overflow: hidden;
}

.status-indicator.paused {
    background: rgba(255, 165, 0, 0.2);
    color: #FFA500;
    border: 1px solid rgba(255, 165, 0, 0.3);
}

@keyframes readingGlow {
    0%, 100% {
        box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
    }
}

/* 朗读中状态的流动银色边框 */
.status-indicator.reading::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, 
        transparent, 
        rgba(192, 192, 192, 0.8), 
        transparent, 
        rgba(192, 192, 192, 0.8), 
        transparent
    );
    background-size: 200% 200%;
    border-radius: 22px;
    z-index: -1;
    animation: silverFlow 3s linear infinite;
}

@keyframes silverFlow {
    0% {
        background-position: 0% 50%;
    }
    100% {
        background-position: 200% 50%;
    }
}

/* 章节信息 */
.chapter-info {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 16px;
}

.chapter-title {
    font-size: 14px;
    font-weight: 500;
    color: #E8E8E8;
    margin-bottom: 4px;
    line-height: 1.4;
}

.chapter-meta {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.5);
}

/* 控制面板 */
.controls-section {
    margin-bottom: 16px;
}

.section-title {
    font-size: 14px;
    font-weight: 500;
    color: #E8E8E8;
    margin-bottom: 12px;
    padding-bottom: 4px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* 语速控制 */
.speed-control {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.speed-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    min-width: 40px;
}

.speed-slider {
    flex: 1;
    height: 4px;
    border-radius: 2px;
    background: rgba(255, 255, 255, 0.2);
    outline: none;
    -webkit-appearance: none;
}

.speed-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #FFD700;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(255, 215, 0, 0.3);
}

.speed-value {
    font-size: 12px;
    color: #FFD700;
    min-width: 30px;
    text-align: right;
}

/* 设置按钮 */
.settings-button {
    width: 100%;
    padding: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
    color: #E8E8E8;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.settings-button:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 215, 0, 0.5);
}

/* 输入框样式 */
.input-group {
    margin-bottom: 16px;
}

.input-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 6px;
    display: block;
}

.input-field {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.05);
    color: #E8E8E8;
    font-size: 14px;
    transition: all 0.3s ease;
}

.input-field:focus {
    outline: none;
    border-color: rgba(255, 215, 0, 0.5);
    background: rgba(255, 255, 255, 0.1);
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.2);
}

.input-field::placeholder {
    color: rgba(255, 255, 255, 0.4);
}

/* 按钮组 */
.button-group {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
}

.button-group .button {
    flex: 1;
    padding: 10px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.05);
    color: #E8E8E8;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.button-group .button:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 215, 0, 0.5);
}

.button-group .button.active {
    background: rgba(255, 215, 0, 0.2);
    border-color: rgba(255, 215, 0, 0.5);
    color: #FFD700;
}

/* 列表样式 */
.list-container {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.02);
}

.list-item {
    padding: 10px 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    cursor: pointer;
    transition: all 0.3s ease;
}

.list-item:last-child {
    border-bottom: none;
}

.list-item:hover {
    background: rgba(255, 255, 255, 0.05);
}

.list-item.selected {
    background: rgba(255, 215, 0, 0.1);
    border-color: rgba(255, 215, 0, 0.3);
}

/* 响应式设计 */
@media (max-width: 320px) {
    .container {
        padding: 12px;
    }

    .title {
        font-size: 18px;
    }

    .play-pause-button {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }

    .button-group {
        flex-direction: column;
    }

    .speed-control {
        flex-direction: column;
        gap: 8px;
    }

    .speed-label,
    .speed-value {
        min-width: auto;
        text-align: center;
    }
}
