# 神灯AI·灵阅 V0.49

AI 驱动的浏览器阅读增强插件，专为网络小说和长文阅读优化，提供智能连续播放、语音朗读和内容管理功能。

## 🎯 核心功能 (已实现)

### 📖 智能连续阅读
*   ✅ **自动章节切换**: 智能识别"下一页"/"下一章"链接，实现无缝续读
*   ✅ **后台播放**: 支持切换页面后继续播放，不中断阅读体验
*   ✅ **章节计数控制**: 可设定播放章节数量限制 (1-999章)
*   ✅ **时长控制**: 可设定播放时长限制 (1-999分钟)

### 🎵 语音朗读系统
*   ✅ **系统TTS集成**: 完整支持浏览器内置语音引擎
*   ✅ **语音管理**: 智能语音列表，支持男声/女声标识
*   ✅ **实时控制**: 播放/暂停、语速调节 (0.5x-10x)、音量控制
*   ✅ **语速记忆**: 自动保存用户偏好设置

### 🌐 URL播放功能
*   ✅ **智能URL输入**: 支持直接输入网址播放指定内容
*   ✅ **播放历史**: 自动记录播放历史，支持快速重播
*   ✅ **状态管理**: 播放时锁定输入，停止时自动清理
*   ✅ **去重机制**: 智能去重，同一小说只保留最新章节

### 🎨 用户界面
*   ✅ **侧边栏设计**: 紧凑型侧边栏界面，不干扰阅读
*   ✅ **实时状态**: 显示当前播放状态、进度和章节信息
*   ✅ **语音设置**: 独立语音设置面板，支持语音试听
*   ✅ **响应式布局**: 适配不同屏幕尺寸

## 🚀 安装使用

### 浏览器兼容性
- ✅ Chrome 88+
- ✅ Edge 88+
- ✅ Firefox (部分功能)

### 安装步骤
1. 下载扩展文件
2. 打开浏览器扩展管理页面
3. 启用"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目根目录

### 使用方法
1. 打开任意网页小说
2. 点击浏览器工具栏中的神灯图标
3. 在侧边栏中点击播放按钮开始朗读
4. 支持输入URL播放指定内容

## 🛠️ 技术架构

### 核心技术
- **Manifest V3**: 现代浏览器扩展标准
- **Service Worker**: 后台事件处理和状态管理
- **Content Scripts**: 页面内容解析和DOM操作
- **Chrome APIs**: TTS、Storage、Tabs等浏览器API

### 项目结构
```
src/
├── background/         # Service Worker后台脚本
├── content/           # 内容脚本 (页面解析)
├── ui/               # 用户界面
│   ├── sidepanel/    # 主侧边栏界面
│   ├── options/      # 选项设置页面
│   └── voiceSettings/ # 语音设置面板
├── lib/              # 第三方库 (Readability.js)
├── assets/           # 静态资源 (图标等)
└── utils/            # 工具函数
```

## 🔮 规划功能

### 短期计划 (V0.5x)
*   🔄 第三方TTS引擎集成 (云端/本地)
*   🎭 AI多角色朗读
*   📊 阅读数据统计

### 长期愿景
*   🧠 AI智能摘要与WIKI构建
*   💬 AI互动问答与剧情预测
*   📈 可视化图谱生成 (人物关系、时间线)
*   📱 PWA应用支持

## 📄 许可证

MIT License
