# 神灯AI·灵阅 - 版本历史

(本文件用于记录版本更新、功能变更和问题修复，请在发布新版本时及时补充)

## [0.49] - 2024-12-19

### 新增
- ✅ 扫把图标清除按钮，无背景框设计
- ✅ 播放状态下URL输入框智能锁定功能
- ✅ 动态提示文字：播放时显示"需要停止播放后才能输入网址"
- ✅ 停止按钮一键清除所有相关状态和缓存

### 改进
- 🔒 播放状态下完全禁用URL输入框，防止用户误操作
- 🎨 清除按钮视觉优化：扫把图标🧹，hover时红色放大效果
- 📝 智能提示文字切换，明确告知用户当前状态和操作方式
- 🧹 停止按钮增强清理逻辑，确保前后端状态一致性

### 修复
- 🐛 修复连续播放可能被URL输入干扰的问题
- 🐛 修复播放状态下用户仍可修改URL导致的状态混乱
- 🐛 修复缓存不一致导致的播放逻辑错误
- 🐛 修复停止后状态未完全清理的问题

### 技术优化
- 🔄 完善的状态管理：播放时锁定，停止时清理
- 💾 智能缓存管理：避免状态冲突，确保播放稳定性
- 🎯 用户体验优化：清晰的状态反馈和操作引导

## [0.36] - 2024-03-20

### 改进
- 优化了TTS停止逻辑，确保在各种情况下可靠停止
- 增强了侧边栏关闭时的停止命令处理
- 改进了状态同步机制，避免UI与实际播放状态不同步
- 添加了定期状态检查，确保TTS状态始终正确

### 修复
- 修复了暂停按钮在页面变化时消失的问题
- 修复了停止按钮在某些情况下无响应的问题
- 修复了关闭侧边栏不能停止播放的问题
- 优化了状态保护逻辑，避免外部事件干扰播放状态

## [0.35] - 2024-03-19

### 改进
- 优化了刷新机制，现在只响应播放按钮触发的刷新
- 改进了自动播放的判断逻辑，避免意外的自动播放
- 增强了标记管理，确保刷新标记能够正确清除
- 优化了UI状态同步，提高了状态显示的准确性

### 修复
- 修复了手动刷新可能触发自动播放的问题
- 修复了刷新后UI状态可能显示不正确的问题
- 修复了多个与刷新相关的状态同步问题

## [版本号] - YYYY-MM-DD

### 新增
- ...

### 修复
- ...

### 变更
- ...
