# 神灯AI·灵阅 - 版本历史

(本文件用于记录版本更新、功能变更和问题修复，请在发布新版本时及时补充)

## [0.36] - 2024-03-20

### 改进
- 优化了TTS停止逻辑，确保在各种情况下可靠停止
- 增强了侧边栏关闭时的停止命令处理
- 改进了状态同步机制，避免UI与实际播放状态不同步
- 添加了定期状态检查，确保TTS状态始终正确

### 修复
- 修复了暂停按钮在页面变化时消失的问题
- 修复了停止按钮在某些情况下无响应的问题
- 修复了关闭侧边栏不能停止播放的问题
- 优化了状态保护逻辑，避免外部事件干扰播放状态

## [0.35] - 2024-03-19

### 改进
- 优化了刷新机制，现在只响应播放按钮触发的刷新
- 改进了自动播放的判断逻辑，避免意外的自动播放
- 增强了标记管理，确保刷新标记能够正确清除
- 优化了UI状态同步，提高了状态显示的准确性

### 修复
- 修复了手动刷新可能触发自动播放的问题
- 修复了刷新后UI状态可能显示不正确的问题
- 修复了多个与刷新相关的状态同步问题

## [版本号] - YYYY-MM-DD

### 新增
- ...

### 修复
- ...

### 变更
- ...
