本文档是项目的入口文件，提供项目的基本介绍、核心功能概述、安装和使用指南、以及当前状态和版本信息。

# 神灯AI·灵阅 V0.49 - 项目概述

## 🎯 项目定位

神灯AI·灵阅是一款专为网络小说和长文阅读优化的浏览器扩展，致力于提供最佳的听书体验。通过智能内容解析、连续播放和语音朗读技术，让用户享受无缝的沉浸式阅读体验。

## ✨ 核心功能 (已实现)

### 📖 智能连续阅读系统
* **自动章节识别**: 智能识别各类小说网站的"下一页"/"下一章"链接
* **无缝续读**: 章节切换后自动继续播放，无需手动干预
* **后台播放**: 支持用户切换页面后继续播放，真正的后台朗读
* **章节计数**: 可设定播放章节数量 (1-999章)，到达后自动停止
* **时长控制**: 可设定播放时长 (1-999分钟)，支持定时停止

### 🎵 专业语音朗读
* **系统TTS集成**: 完整支持Chrome/Edge内置语音引擎
* **智能语音管理**: 自动识别男声/女声，支持语音收藏
* **实时控制**: 播放/暂停、语速调节 (0.5x-10x)、音量控制
* **设置记忆**: 自动保存用户的语音和语速偏好

### 🌐 URL播放功能
* **智能URL输入**: 支持直接输入网址播放任意网页内容
* **播放历史管理**: 自动记录播放历史，支持快速重播和删除
* **状态智能管理**: 播放时锁定输入框，停止时自动清理状态
* **去重优化**: 同一小说只保留最新播放的章节记录

### 🎨 用户界面设计
* **紧凑侧边栏**: 不干扰阅读的侧边栏设计，支持折叠展开
* **实时状态显示**: 清晰显示播放状态、进度、章节信息
* **独立语音设置**: 专门的语音设置面板，支持语音试听
* **响应式布局**: 适配不同屏幕尺寸，优化空间利用

## 🛠️ 技术实现

### 架构设计
* **Manifest V3**: 采用最新的浏览器扩展标准
* **Service Worker**: 后台事件处理和状态管理
* **Content Scripts**: 页面内容解析和DOM操作
* **模块化设计**: 清晰的代码结构，便于维护和扩展

### 核心模块
* **背景脚本** (`src/background/`): 播放控制、状态管理、消息处理
* **内容解析** (`src/content/`): 页面内容提取、下一页识别
* **用户界面** (`src/ui/`): 侧边栏、设置页面、语音管理
* **工具库** (`src/lib/`, `src/utils/`): 通用功能和第三方库

## 📊 当前状态与版本

### 版本信息
- **当前版本**: V0.49
- **发布日期**: 2024-12-19
- **开发状态**: 稳定版本，核心功能完整

### 功能完成度
- ✅ **基础阅读功能**: 100% (连续播放、语音控制)
- ✅ **URL播放功能**: 100% (输入播放、历史管理)
- ✅ **用户界面**: 100% (侧边栏、设置面板)
- ✅ **状态管理**: 100% (播放状态、缓存管理)
- 🔄 **第三方TTS**: 规划中
- 🔄 **AI功能**: 规划中

## 🚀 安装与使用

### 系统要求
- Chrome 88+ / Edge 88+ / Firefox (部分功能)
- 支持TTS的操作系统 (Windows 10+, macOS, Linux)

### 安装步骤
1. 下载项目文件到本地
2. 打开浏览器扩展管理页面 (`chrome://extensions/`)
3. 启用"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目根目录完成安装

### 使用指南
1. **基础播放**: 打开小说页面 → 点击扩展图标 → 点击播放按钮
2. **URL播放**: 在侧边栏输入框中输入网址 → 点击播放
3. **设置调节**: 点击设置按钮调整语音、语速等参数
4. **历史管理**: 查看播放历史，快速重播之前的内容

## 📈 发展规划

### 近期目标 (V0.5x)
- 集成第三方TTS引擎 (Azure, Google Cloud)
- 支持本地TTS模型 (LM Studio集成)
- 添加阅读数据统计功能

### 长期愿景
- AI智能摘要与内容理解
- 多角色语音朗读
- 可视化阅读分析
- 跨设备同步功能
