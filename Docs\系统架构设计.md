一、 系统架构 (System Architecture) - 基于 Manifest V3
我们将采用现代浏览器扩展的标准架构 (Manifest V3)，主要包含以下几个部分：

Service Worker (Background):
职责: 作为扩展的事件处理中心，处理浏览器事件 (如安装、标签页更新)、管理扩展的长期状态、与外部 API (TTS, LLM) 通信、协调其他部分。在 Manifest V3 中，它取代了持久的背景页，按需运行。
技术: JavaScript 或 TypeScript。
Content Scripts:
职责: 注入到用户浏览的网页中，直接与页面的 DOM 交互。负责抓取小说/文章内容、识别“下一页/章”按钮、高亮正在朗读的文本、将用户操作（如选中文本）通知给 Service Worker。
技术: JavaScript 或 TypeScript。
UI (用户界面):
Side Panel (首选) / Popup:
职责: 提供主要的交互界面，用于显示阅读控制按钮 (播放/暂停/语速/音量)、语音选择、阅读进度、设置入口，以及展示 AI 功能的结果 (WIKI 查询、摘要、图谱等)。Side Panel API (较新) 允许更持久和宽敞的 UI 空间，比 Popup 更适合复杂交互。
技术: HTML, CSS, JavaScript/TypeScript。可以考虑使用轻量级 UI 框架 (如 React, Vue, Svelte, Preact) 来提高开发效率和组件化。
Options Page:
职责: 提供更详细的配置选项，如 API Key 设置、本地模型端点配置、默认语音偏好、功能开关等。
技术: HTML, CSS, JavaScript/TypeScript。
Storage:
职责: 持久化存储用户设置、阅读进度、收藏的语音、API Keys (安全存储) 等。
技术: 使用 chrome.storage.local (本地存储) 或 chrome.storage.sync (跨设备同步，有容量限制)。敏感信息（如 API Key）需要额外加密处理或通过更安全的方式管理。
通讯机制:
各部分之间通过 chrome.runtime.sendMessage 和 chrome.tabs.sendMessage 以及相关的监听器 (onMessage) 进行通信。
核心逻辑/模块 (Core Logic / Modules):
职责: 封装可重用的核心功能，如：
PageParser: 网页内容解析与下一页/章识别逻辑。
TTSManager: 抽象不同 TTS 引擎（系统、云、本地）的接口，管理语音列表和播放。
LLMManager: 抽象与 LLM API 或本地模型的交互，处理摘要、WIKI 构建、问答、预测等任务。
StateManager: 管理应用的核心状态（阅读状态、设置等）。
WikiBuilder: 负责从文本构建和查询 WIKI 数据。
RoleRecognizer: 负责识别对话角色。
技术: JavaScript/TypeScript。这些模块会被 Service Worker、UI 或 Content Scripts 按需调用。
二、 目录结构 (Directory Structure)
CopyInsert
神灯AI·灵阅/
├── Docs/                  # 项目文档
│   ├── README(项目说明).md
│   ├── REQUIREMENTS(需求规格).md
│   ├── DESIGN(设计文档).md
│   ├── DEVELOPMENT(开发指南).md
│   ├── FEATURES(功能实现).md
│   ├── CHANGELOG(更新日志).md
│   └── 系统架构设计.md
├── 存档/                  # 历史文档存档
├── scripts/               # 构建、部署等辅助脚本
│   └── build.js           # (示例)
├── src/                   # 源代码 (实际结构)
│   ├── background/        # Service Worker 后台脚本
│   │   ├── index.js       # 主Service Worker (3600+行)
│   │   ├── minimal.js     # 精简版本
│   │   ├── readingState.mjs # 状态管理模块
│   │   └── test-simple.js # 测试脚本
│   ├── content/           # 内容脚本
│   │   ├── parser.js      # 页面内容解析 (Readability集成)
│   │   ├── parser-simple.js # 简化解析器
│   │   ├── injector.js    # 页面注入脚本
│   │   └── page-fixer.js  # 页面修复工具
│   ├── core/              # 核心功能模块
│   │   ├── ttsManager.js  # TTS管理器
│   │   └── stateManager.js # 状态管理器
│   ├── lib/               # 第三方库
│   │   └── Readability.js # Mozilla Readability库
│   ├── ui/                # 用户界面
│   │   ├── sidepanel/     # 主侧边栏界面
│   │   │   ├── index.html # 主界面HTML
│   │   │   ├── index.js   # 主界面逻辑 (4400+行)
│   │   │   ├── styles.css # 主样式
│   │   │   ├── url-input-styles.css # URL输入样式
│   │   │   └── compact-styles.css # 紧凑布局样式
│   │   ├── options/       # 选项设置页面
│   │   │   ├── index.html
│   │   │   ├── index.js
│   │   │   └── styles.css
│   │   ├── voiceSettings/ # 语音设置面板
│   │   │   ├── index.html
│   │   │   ├── index.js
│   │   │   └── styles.css
│   │   ├── voiceSettingsPanel/ # 语音设置弹窗
│   │   ├── popup/         # 弹窗界面 (备用)
│   │   └── sideTabs/      # 侧边标签 (备用)
│   ├── utils/             # 工具函数
│   │   ├── storage.js     # 存储封装
│   │   └── helpers.js     # 辅助函数
│   └── assets/            # 静态资源
│       ├── icons/         # 扩展图标
│       │   ├── icon16.png
│       │   ├── icon32.png
│       │   ├── icon48.png
│       │   └── icon128.png
│       └── all.gif        # 动画资源
├── ui/                    # 独立UI组件 (备用)
│   └── voiceSettings/     # 语音设置组件
├── test-*.html            # 测试页面
├── URL_PLAYBACK_MODULE.md # URL播放模块文档
├── manifest.json          # 扩展配置文件 (Manifest V3)
├── package.json           # 项目配置
└── README.md              # 项目说明
三、 文件命名规则 (File Naming Conventions)
目录 (Folders): 使用 kebab-case (短横线连接的小写字母)，例如 background, content-scripts, ui-components。
JavaScript/TypeScript 文件:
普通脚本、模块、工具函数: 使用 camelCase.js (驼峰命名)，例如 ttsManager.js, pageParser.js, utilsHelper.js。
UI 组件文件 (若使用 React/Vue 等): 使用 PascalCase.jsx 或 PascalCase.vue (大驼峰)，例如 ReaderControls.jsx, SettingsForm.vue。
入口文件: 可使用 index.js。
CSS/SCSS 文件: 使用 kebab-case.css 或 styles.css，例如 sidepanel-styles.css。
HTML 文件: 使用 kebab-case.html 或 index.html，例如 options-page.html。
类 (Classes): 使用 PascalCase。
函数 (Functions) 和变量 (Variables): 使用 camelCase。
常量 (Constants): 使用 UPPER_SNAKE_CASE (全大写下划线连接)，例如 DEFAULT_SPEED, API_ENDPOINT。
建议:

技术选型: 建议使用 TypeScript 以获得更好的类型检查和代码提示，尤其对于中大型项目。考虑使用 Webpack 或 Vite 等现代构建工具来处理模块打包、代码转换 (TS/JSX) 和资源管理。
一致性: 最重要的是在整个项目中保持命名和结构风格的一致性。
这套架构和规范为您提供了一个清晰的起点。您可以根据项目的具体进展和技术选型进行微调。接下来我们可以开始创建这些基础目录和配置文件了。