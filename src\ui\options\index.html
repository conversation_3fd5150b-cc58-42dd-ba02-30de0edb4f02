<!DOCTYPE html>
<html>
<head>
    <title>神灯 AI 阅读助手 - 设置</title>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <h1>神灯 AI 阅读助手 - 设置</h1>

        <section>
            <h2>基本设置</h2>
            <div class="option">
                <label for="default-speed">默认语速:</label>
                <input type="range" id="default-speed" min="0.5" max="3" step="0.1" value="1.0">
                <span id="default-speed-value">1.0x</span>
            </div>
             <div class="option">
                 <label for="default-voice">默认语音:</label>
                 <select id="default-voice">
                     <option value="">加载中...</option>
                 </select>
             </div>
             <div class="option">
                 <label for="default-continuous-reading">默认开启连续阅读:</label>
                 <input type="checkbox" id="default-continuous-reading" checked>
            </div>
        </section>

        <section>
            <h2>TTS 引擎 (规划中)</h2>
            <div class="option">
                <label for="tts-engine-select">优先使用的 TTS 引擎:</label>
                <select id="tts-engine-select" disabled>
                    <option value="browser">浏览器内置 TTS</option>
                    <!-- <option value="azure">Azure TTS (需要配置)</option> -->
                    <!-- <option value="google">Google Cloud TTS (需要配置)</option> -->
                </select>
            </div>
             <div class="option api-key-config" style="display: none;">
                 <label for="api-key-input">API 密钥:</label>
                 <input type="password" id="api-key-input" placeholder="输入您的 API Key">
                 <span class="api-status">未配置</span>
             </div>
        </section>

        <section>
             <h2>关于</h2>
             <p>版本: <span id="version">0.36</span></p>
             <!-- Add links, credits etc. here -->
        </section>

        <button id="save-button">保存设置</button>
        <p id="save-status"></p>

    </div>
    <script src="index.js"></script>
</body>
</html>
